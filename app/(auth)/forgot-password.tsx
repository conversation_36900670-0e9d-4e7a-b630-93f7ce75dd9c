import React, { useState, useEffect } from 'react';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { HeaderText, SubtitleText } from '@/components/ui/Typography';
import { Feather } from '@expo/vector-icons';

export default function ForgotPasswordScreen() {
  const { state, forgotPassword, clearError } = useAuth();

  const [email, setEmail] = useState('');
  const [formError, setFormError] = useState<string | null>(null);

  // Clear any errors when component mounts and set page title
  useEffect(() => {
    clearError();
    if (typeof document !== 'undefined') {
      document.title = 'Reset Password - CasaPay';
    }
  }, []);

  const validateForm = () => {
    if (!email.trim()) {
      setFormError('Email is required');
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setFormError('Email is invalid');
      return false;
    }

    setFormError(null);
    return true;
  };

  const handleForgotPassword = async () => {
    // Form validation
    if (!validateForm()) {
      return;
    }

    // Call forgotPassword from auth context
    await forgotPassword(email);
  };

  const handleBackToLogin = () => {
    router.push('/(auth)/login');
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pt-10 pb-10">
        <StyledView className="w-full max-w-md mx-auto">
          {/* Back button */}
          <StyledView className="mb-6">
            <TouchableOpacity
              onPress={handleBackToLogin}
              className="p-2 self-start"
              disabled={state.isLoading}
            >
              <Feather name="arrow-left" size={24} color="#374151" />
            </TouchableOpacity>
          </StyledView>

          <StyledView className="mb-12">
            <HeaderText>Reset your password</HeaderText>
            <SubtitleText>Enter your email address and we'll send you instructions to reset your password</SubtitleText>
          </StyledView>

          <StyledView className="w-full">
            {/* Error message from auth context */}
            {state.error ? (
              <StyledView className="mb-6 py-3 px-5 bg-red-100 rounded-md">
                <StyledText className="text-red-500 text-sm">{state.error}</StyledText>
              </StyledView>
            ) : null}

            {/* Form validation error */}
            {formError ? (
              <StyledView className="mb-6 py-3 px-5 bg-red-100 rounded-md">
                <StyledText className="text-red-500 text-sm">{formError}</StyledText>
              </StyledView>
            ) : null}

            {/* Email Field */}
            <StyledTextInput
              label="Email address"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!state.isLoading}
              error={formError || undefined}
            />

            {/* Submit Button */}
            <StyledTouchableOpacity
              className={`bg-secondary rounded-xl py-4 items-center w-full shadow-button-secondary ${state.isLoading ? 'opacity-70' : ''}`}
              onPress={handleForgotPassword}
              disabled={state.isLoading}
            >
              {state.isLoading ? (
                <ActivityIndicator color="white" size="small" />
              ) : (
                <StyledText className="text-white text-base font-medium">Send Reset Instructions</StyledText>
              )}
            </StyledTouchableOpacity>

            {/* Back to Log In Link */}
            <StyledTouchableOpacity
              className="items-center mt-8"
              onPress={handleBackToLogin}
            >
              <StyledText className="text-link text-sm">Back to Log In</StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
