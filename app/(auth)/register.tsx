import React, { useState, useEffect } from 'react';
import { ActivityIndicator, TouchableOpacity, Linking, Platform, Text, TextInput, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { StyledText, StyledView, StyledTextInput, StyledTouchableOpacity, StyledScrollView, StyledSafeAreaView } from '@/components/ui/StyledComponents';
import { Feather } from '@expo/vector-icons';
import { PhotoCapture } from '@/components/ui/PhotoCapture';

type RegistrationStep = 'email' | 'pin' | 'name' | 'country-residence' | 'phone-input' | 'phone-verification' | 'password-creation' | 'employment' | 'income' | 'plan' | 'kyc-selfie' | 'kyc-id-front' | 'kyc-id-back' | 'income-verification' | 'placeholder' | 'done';

// React Native StyleSheet for guaranteed style application
const nativeStyles = StyleSheet.create({
  textInputBase: {
    width: '100%',
    height: 48, // Exact pixel height
    paddingHorizontal: 16, // Exact horizontal padding
    paddingVertical: 0, // Remove vertical padding to control height precisely
    borderWidth: 1,
    borderRadius: 12,
    backgroundColor: '#ffffff',
    fontSize: 16,
    color: '#000000',
    // Critical React Native text alignment properties
    textAlignVertical: 'center',
    includeFontPadding: false,
    // Remove any default margins/padding
    margin: 0,
    padding: 0,
  },
  textInputNormal: {
    textAlign: 'left',
    fontFamily: 'System',
    letterSpacing: 0,
  },
  textInputCentered: {
    textAlign: 'center',
    fontFamily: 'monospace',
    letterSpacing: 2,
    fontSize: 18, // Slightly larger for PIN
  },
  borderNormal: {
    borderColor: '#d1d5db', // gray-300
  },
  borderError: {
    borderColor: '#ef4444', // red-500
  },
});

// Custom TextInput component moved outside to prevent recreation on re-renders
const CustomTextInput: React.FC<{
  hasError: boolean;
  additionalClasses?: string;
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  keyboardType?: any;
  autoCapitalize?: any;
  editable?: boolean;
  maxLength?: number;
  secureTextEntry?: boolean;
  isWebPlatform: boolean;
}> = ({ hasError, additionalClasses = '', isWebPlatform, ...props }) => {
  // Extract styling intentions from additionalClasses
  const hasTextCenter = additionalClasses.includes('text-center');
  const hasFontMono = additionalClasses.includes('font-mono');
  const hasTrackingWidest = additionalClasses.includes('tracking-widest');

  if (isWebPlatform) {
    // Web platform: Use CSS classes as intended
    const baseClasses = "w-full h-12 border rounded-xl px-4 py-3 bg-white transition-colors";
    const errorClasses = hasError
      ? 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-200'
      : 'border-gray-300 focus:border-secondary focus:ring-2 focus:ring-secondary/20';
    const className = `${baseClasses} ${errorClasses} focus:outline-none ${additionalClasses}`;

    return (
      <TextInput
        className={className}
        placeholderTextColor="#9B9B9B"
        {...props}
      />
    );
  } else {
    // React Native platform: Use StyleSheet.create() for guaranteed style application
    const styleArray = [
      nativeStyles.textInputBase,
      hasTextCenter ? nativeStyles.textInputCentered : nativeStyles.textInputNormal,
      hasError ? nativeStyles.borderError : nativeStyles.borderNormal,
    ];

    // Debug logging for React Native
    if (__DEV__) {
      console.log('CustomTextInput React Native Debug:', {
        hasTextCenter,
        hasFontMono,
        hasTrackingWidest,
        additionalClasses,
        styleArray: styleArray.length,
      });
    }

    return (
      <TextInput
        placeholderTextColor="#9B9B9B"
        style={styleArray}
        // Additional React Native specific props for text alignment
        textAlign={hasTextCenter ? 'center' : 'left'}
        textAlignVertical="center"
        {...props}
      />
    );
  }
};

interface FormData {
  email: string;
  pin: string;
  name: string;
  countryOfResidence: string;
  phoneCountryCode: string;
  phoneNumber: string;
  phoneVerified: boolean;
  phonePin: string;
  password: string;
  passwordConfirmation: string;
  employment: string;
  income: string;
  plan: string;
  kycDocuments: {
    selfie?: string;
    idFront?: string;
    idBack?: string;
  };
  incomeVerification: {
    type?: 'banking' | 'document';
    completed?: boolean;
  };
  skipStates: {
    plan?: boolean;
    kycSelfie?: boolean;
    kycIdFront?: boolean;
    kycIdBack?: boolean;
    incomeVerification?: boolean;
  };
}



// Country data for residence selection
const COUNTRIES = [
  { code: 'US', name: 'United States', flag: '🇺🇸', phoneCode: '+1' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', phoneCode: '+44' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦', phoneCode: '+1' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', phoneCode: '+61' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪', phoneCode: '+49' },
  { code: 'FR', name: 'France', flag: '🇫🇷', phoneCode: '+33' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸', phoneCode: '+34' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹', phoneCode: '+39' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱', phoneCode: '+31' },
  { code: 'BE', name: 'Belgium', flag: '🇧🇪', phoneCode: '+32' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭', phoneCode: '+41' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹', phoneCode: '+43' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪', phoneCode: '+46' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴', phoneCode: '+47' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰', phoneCode: '+45' },
  { code: 'FI', name: 'Finland', flag: '🇫🇮', phoneCode: '+358' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬', phoneCode: '+65' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵', phoneCode: '+81' },
  { code: 'KR', name: 'South Korea', flag: '🇰🇷', phoneCode: '+82' },
  { code: 'IE', name: 'Ireland', flag: '🇮🇪', phoneCode: '+353' },
];

export default function TenantRegisterScreen() {
  const { state, clearError, login } = useAuth();

  // Platform detection inside component to ensure proper initialization
  const isWebPlatform = Platform.OS === 'web' || typeof window !== 'undefined';

  // Custom header and subtitle components that bypass StyledText type system
  const HeaderText: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    if (isWebPlatform) {
      return (
        <Text className="text-4xl font-bold text-gray-900 mb-3 leading-tight">
          {children}
        </Text>
      );
    } else {
      // React Native: Use explicit style object for guaranteed large text
      return (
        <Text style={{
          fontSize: 32,
          lineHeight: 38,
          fontWeight: '700',
          color: '#111827', // gray-900
          marginBottom: 12
        }}>
          {children}
        </Text>
      );
    }
  };

  const SubtitleText: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    if (isWebPlatform) {
      return (
        <Text className="text-lg text-gray-600 leading-relaxed">
          {children}
        </Text>
      );
    } else {
      // React Native: Use explicit style object for guaranteed styling
      return (
        <Text style={{
          fontSize: 18,
          lineHeight: 26,
          color: '#4B5563', // gray-600
          marginBottom: 8
        }}>
          {children}
        </Text>
      );
    }
  };

  // Reusable SkipCheckbox component
  const SkipCheckbox: React.FC<{
    isChecked: boolean;
    onToggle: () => void;
    label: string;
    disabled?: boolean;
  }> = ({ isChecked, onToggle, label, disabled = false }) => {
    return (
      <StyledTouchableOpacity
        className="w-full py-3 items-center flex-row"
        onPress={onToggle}
        disabled={disabled || isLoading}
        style={{ minHeight: 44 }} // Accessibility touch target
      >
        {/* Checkbox */}
        <StyledView
          className={`w-5 h-5 border-2 rounded mr-2 items-center justify-center ${
            isChecked
              ? 'border-secondary bg-secondary'
              : 'border-gray-300 bg-white'
          }`}
        >
          {isChecked && (
            <Feather name="check" size={14} color="white" />
          )}
        </StyledView>

        {/* Label */}
        <StyledText className="text-gray-600 text-base flex-1">
          {label}
        </StyledText>
      </StyledTouchableOpacity>
    );
  };





  const [currentStep, setCurrentStep] = useState<RegistrationStep>('email');
  const [formData, setFormData] = useState<FormData>({
    email: '',
    pin: '',
    name: '',
    countryOfResidence: '',
    phoneCountryCode: '',
    phoneNumber: '',
    phoneVerified: false,
    phonePin: '',
    password: '',
    passwordConfirmation: '',
    employment: '',
    income: '',
    plan: '',
    kycDocuments: {},
    incomeVerification: {},
    skipStates: {}
  });
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [isLoading, setIsLoading] = useState(false);

  // State for phone verification timer
  const [resendTimer, setResendTimer] = useState(0);

  // State for password visibility
  const [showPassword, setShowPassword] = useState(false);
  const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

  // Clear any errors when component mounts
  useEffect(() => {
    clearError();
  }, []);

  // Handle return from income verification subflow
  useEffect(() => {
    const handleIncomeVerificationReturn = () => {
      // Check if we're returning from income verification
      const urlParams = new URLSearchParams(window.location.search);
      const incomeVerificationCompleted = urlParams.get('incomeVerificationCompleted');
      const method = urlParams.get('method');

      if (incomeVerificationCompleted === 'true') {
        // Update form data to mark income verification as completed
        setFormData(prev => ({
          ...prev,
          incomeVerification: {
            type: method as 'banking' | 'document',
            completed: true
          }
        }));

        // Move to next step
        setCurrentStep('placeholder');

        // Clean up URL params
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    };

    // Only run on web platform
    if (typeof window !== 'undefined') {
      handleIncomeVerificationReturn();
    }
  }, []);

  // Handle phone verification timer
  useEffect(() => {
    if (currentStep === 'phone-verification' && resendTimer === 0) {
      setResendTimer(30);
    }
  }, [currentStep]);

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  // Handle Terms of Use and Privacy Policy links
  const handleTermsOfUse = () => {
    // TODO: Replace with actual Terms of Use URL
    Linking.openURL('https://casapay.com/terms');
  };

  const handlePrivacyPolicy = () => {
    // TODO: Replace with actual Privacy Policy URL
    Linking.openURL('https://casapay.com/privacy');
  };

  // Update form data helper
  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear any existing error for this field
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Helper function to handle skip actions
  const handleSkipStep = (stepKey: keyof FormData['skipStates']) => {
    setFormData(prev => ({
      ...prev,
      skipStates: {
        ...prev.skipStates,
        [stepKey]: true
      }
    }));
  };

  // Helper function to clear skip state
  const clearSkipStep = (stepKey: keyof FormData['skipStates']) => {
    setFormData(prev => ({
      ...prev,
      skipStates: {
        ...prev.skipStates,
        [stepKey]: false
      }
    }));
  };

  // Helper function to toggle skip state
  const toggleSkipStep = (stepKey: keyof FormData['skipStates']) => {
    const isCurrentlySkipped = formData.skipStates[stepKey];
    if (isCurrentlySkipped) {
      clearSkipStep(stepKey);
    } else {
      handleSkipStep(stepKey);
    }
  };

  // Helper function to check if current step can proceed
  const canProceedFromCurrentStep = (): boolean => {
    switch (currentStep) {
      case 'email':
        return !!formData.email.trim() && /\S+@\S+\.\S+/.test(formData.email);
      case 'pin':
        return formData.pin.length === 6;
      case 'name':
        return !!formData.name.trim();
      case 'country-residence':
        return !!formData.countryOfResidence.trim();
      case 'phone-input':
        return !!formData.phoneCountryCode.trim() && !!formData.phoneNumber.trim();
      case 'phone-verification':
        return formData.phonePin.length === 4;
      case 'password-creation':
        return !!formData.password.trim() && !!formData.passwordConfirmation.trim() &&
               formData.password === formData.passwordConfirmation && formData.password.length >= 8;
      case 'employment':
        return !!formData.employment.trim();
      case 'income':
        return !!formData.income.trim();
      case 'plan':
        return !!formData.plan.trim() || !!formData.skipStates.plan;
      case 'kyc-selfie':
        return !!formData.kycDocuments.selfie || !!formData.skipStates.kycSelfie;
      case 'kyc-id-front':
        return !!formData.kycDocuments.idFront || !!formData.skipStates.kycIdFront;
      case 'kyc-id-back':
        return !!formData.kycDocuments.idBack || !!formData.skipStates.kycIdBack;
      case 'income-verification':
        return !!formData.incomeVerification.completed || !!formData.skipStates.incomeVerification;
      default:
        return true; // For other steps, use existing validation
    }
  };

  const validateCurrentStep = (): boolean => {
    const errors: {[key: string]: string} = {};

    switch (currentStep) {
      case 'email':
        if (!formData.email.trim()) {
          errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          errors.email = 'Email is invalid';
        }
        break;
      case 'pin':
        if (!formData.pin.trim()) {
          errors.pin = 'Confirmation code is required';
        } else if (formData.pin.length !== 6) {
          errors.pin = 'Confirmation code must be 6 digits';
        }
        break;
      case 'name':
        if (!formData.name.trim()) {
          errors.name = 'Name is required';
        }
        break;
      case 'country-residence':
        if (!formData.countryOfResidence.trim()) {
          errors.countryOfResidence = 'Country of residence is required';
        }
        break;
      case 'phone-input':
        if (!formData.phoneCountryCode.trim()) {
          errors.phoneCountryCode = 'Country code is required';
        }
        if (!formData.phoneNumber.trim()) {
          errors.phoneNumber = 'Phone number is required';
        }
        break;
      case 'phone-verification':
        if (!formData.phonePin.trim()) {
          errors.phonePin = 'Verification code is required';
        } else if (formData.phonePin.length !== 4) {
          errors.phonePin = 'Verification code must be 4 digits';
        }
        break;
      case 'password-creation':
        if (!formData.password.trim()) {
          errors.password = 'Password is required';
        } else if (formData.password.length < 8) {
          errors.password = 'Password must be at least 8 characters';
        } else if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password)) {
          errors.password = 'Password must contain at least one letter and one number';
        }
        if (!formData.passwordConfirmation.trim()) {
          errors.passwordConfirmation = 'Password confirmation is required';
        } else if (formData.password !== formData.passwordConfirmation) {
          errors.passwordConfirmation = 'Passwords do not match';
        }
        break;
      case 'employment':
        if (!formData.employment.trim()) {
          errors.employment = 'Employment status is required';
        }
        break;
      case 'income':
        if (!formData.income.trim()) {
          errors.income = 'Income range is required';
        }
        break;
      case 'plan':
        // Plan selection is optional with skip functionality
        break;
      case 'kyc-selfie':
        // KYC selfie is optional, no validation required
        break;
      case 'kyc-id-front':
        // KYC ID front is optional, no validation required
        break;
      case 'kyc-id-back':
        // KYC ID back is optional, no validation required
        break;
      case 'income-verification':
        // Income verification is optional, no validation required
        break;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleContinue = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    setIsLoading(true);

    try {
      if (currentStep === 'email') {
        // TODO: Send email confirmation PIN
        // await sendEmailConfirmation(formData.email);
        setCurrentStep('pin');
      } else if (currentStep === 'pin') {
        // TODO: Verify PIN
        // await verifyEmailPin(formData.email, formData.pin);
        setCurrentStep('name');
      } else if (currentStep === 'name') {
        setCurrentStep('country-residence');
      } else if (currentStep === 'country-residence') {
        // Set default phone country code based on selected country
        if (formData.countryOfResidence && !formData.phoneCountryCode) {
          const countryCodeMap: {[key: string]: string} = {
            'US': '+1', 'CA': '+1', 'GB': '+44', 'DE': '+49', 'FR': '+33',
            'ES': '+34', 'IT': '+39', 'AU': '+61', 'SG': '+65'
          };
          updateFormData('phoneCountryCode', countryCodeMap[formData.countryOfResidence] || '+1');
        }
        setCurrentStep('phone-input');
      } else if (currentStep === 'phone-input') {
        // TODO: Send SMS verification code
        // await sendSMSVerification(formData.phoneCountryCode + formData.phoneNumber);
        setCurrentStep('phone-verification');
      } else if (currentStep === 'phone-verification') {
        // TODO: Verify SMS PIN
        // await verifySMSPin(formData.phoneCountryCode + formData.phoneNumber, formData.phonePin);
        updateFormData('phoneVerified', true);
        setCurrentStep('password-creation');
      } else if (currentStep === 'password-creation') {
        setCurrentStep('employment');
      } else if (currentStep === 'employment') {
        setCurrentStep('income');
      } else if (currentStep === 'income') {
        setCurrentStep('plan');
      } else if (currentStep === 'plan') {
        setCurrentStep('kyc-selfie');
      } else if (currentStep === 'kyc-selfie') {
        setCurrentStep('kyc-id-front');
      } else if (currentStep === 'kyc-id-front') {
        setCurrentStep('kyc-id-back');
      } else if (currentStep === 'kyc-id-back') {
        setCurrentStep('income-verification');
      } else if (currentStep === 'income-verification') {
        // Income verification is handled by direct navigation from the selection
        // This step should not auto-advance
        return;
      } else if (currentStep === 'placeholder') {
        setCurrentStep('done');
      } else if (currentStep === 'done') {
        // Handle completion based on email
        if (formData.email === '<EMAIL>') {
          // For demo user, automatically log them in
          try {
            await login(formData.email, 'any-password'); // Demo login only checks email
            // login function will handle navigation to dashboard
          } catch (error) {
            // If login fails, navigate to login page with success message
            router.replace({
              pathname: '/(auth)/login',
              params: { message: `Welcome ${formData.name}! Please log in to continue.` }
            });
          }
        } else {
          // For other users, redirect to login with success message
          router.replace({
            pathname: '/(auth)/login',
            params: { message: `Welcome ${formData.name}! Please log in to continue.` }
          });
        }
      }
    } catch (error) {
      setFormErrors({
        general: error instanceof Error ? error.message : 'An error occurred. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (currentStep === 'email') {
      router.replace('/(welcome)');
    } else if (currentStep === 'pin') {
      setCurrentStep('email');
    } else if (currentStep === 'name') {
      setCurrentStep('pin');
    } else if (currentStep === 'country-residence') {
      setCurrentStep('name');
    } else if (currentStep === 'phone-input') {
      setCurrentStep('country-residence');
    } else if (currentStep === 'phone-verification') {
      setCurrentStep('phone-input');
    } else if (currentStep === 'password-creation') {
      setCurrentStep('phone-verification');
    } else if (currentStep === 'employment') {
      setCurrentStep('password-creation');
    } else if (currentStep === 'income') {
      setCurrentStep('employment');
    } else if (currentStep === 'plan') {
      setCurrentStep('income');
    } else if (currentStep === 'kyc-selfie') {
      setCurrentStep('plan');
    } else if (currentStep === 'kyc-id-front') {
      setCurrentStep('kyc-selfie');
    } else if (currentStep === 'kyc-id-back') {
      setCurrentStep('kyc-id-front');
    } else if (currentStep === 'income-verification') {
      setCurrentStep('kyc-id-back');
    } else if (currentStep === 'placeholder') {
      setCurrentStep('income-verification');
    } else if (currentStep === 'done') {
      setCurrentStep('placeholder');
    }
  };

  const getStepTitle = (): string => {
    switch (currentStep) {
      case 'email':
        return 'Enter your email address';
      case 'pin':
        return 'Check your email';
      case 'name':
        return 'What\'s your name?';
      case 'country-residence':
        return 'Your primary country of residence';
      case 'phone-input':
        return 'Verify your phone number';
      case 'phone-verification':
        return 'Check your phone';
      case 'password-creation':
        return 'Create your password';
      case 'employment':
        return 'Employment Status';
      case 'income':
        return 'Income Range';
      case 'plan':
        return 'Choose Your Plan';
      case 'kyc-selfie':
        return 'Take a Selfie';
      case 'kyc-id-front':
        return 'Upload ID Front';
      case 'kyc-id-back':
        return 'Upload ID Back';
      case 'income-verification':
        return 'Income Verification';
      case 'placeholder':
        return 'Almost there!';
      case 'done':
        return 'Welcome to CasaPay!';
      default:
        return '';
    }
  };

  const getStepSubtitle = (): string => {
    switch (currentStep) {
      case 'email':
        return 'We\'ll send you a confirmation code';
      case 'pin':
        return `We sent a 6-digit code to ${formData.email}`;
      case 'name':
        return 'Tell us what to call you';
      case 'country-residence':
        return 'Select the country where you currently live';
      case 'phone-input':
        return 'We\'ll send you a verification code';
      case 'phone-verification':
        return `We sent a 4-digit code to ${formData.phoneCountryCode} ${formData.phoneNumber}`;
      case 'password-creation':
        return 'Choose a secure password for your account';
      case 'employment':
        return 'Select your current employment status';
      case 'income':
        return 'Choose your monthly income range';
      case 'plan':
        return 'Select the plan that works best for you';
      case 'kyc-selfie':
        return 'Take a clear photo of yourself for identity verification (optional)';
      case 'kyc-id-front':
        return 'Upload the front side of your government-issued ID (optional)';
      case 'kyc-id-back':
        return 'Upload the back side of your government-issued ID (optional)';
      case 'income-verification':
        return 'Verify your income for better offers (optional)';
      case 'placeholder':
        return 'We\'re setting up your account';
      case 'done':
        return 'Your account has been created successfully';
      default:
        return '';
    }
  };

  const getCTAText = (): string => {
    switch (currentStep) {
      case 'income-verification':
        return 'Continue';
      case 'done':
        return 'Get Started';
      default:
        return 'Continue';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'email':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>
                Enter your email address
              </HeaderText>
              <SubtitleText>
                We'll send you a verification code to get started
              </SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Your email</StyledText>
              <CustomTextInput
                hasError={!!formErrors.email}
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!isLoading}
                isWebPlatform={isWebPlatform}
              />
              {formErrors.email ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.email}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'pin':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Check your email</HeaderText>
              <SubtitleText>We sent a 6-digit code to {formData.email}</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Confirmation code</StyledText>
              <CustomTextInput
                hasError={!!formErrors.pin}
                additionalClasses="text-center text-lg font-mono tracking-widest"
                placeholder="000000"
                value={formData.pin}
                onChangeText={(value) => updateFormData('pin', value)}
                keyboardType="number-pad"
                maxLength={6}
                editable={!isLoading}
                isWebPlatform={isWebPlatform}
              />
              {formErrors.pin ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.pin}</StyledText>
              ) : null}
              <StyledTouchableOpacity className="mt-4" onPress={() => setCurrentStep('email')}>
                <StyledText className="text-secondary text-sm font-medium">Didn't receive the code? Try again</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        );

      case 'name':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>What's your name?</HeaderText>
              <SubtitleText>Tell us what to call you</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Full name</StyledText>
              <CustomTextInput
                hasError={!!formErrors.name}
                placeholder="Enter your full name"
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                editable={!isLoading}
                isWebPlatform={isWebPlatform}
              />
              {formErrors.name ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.name}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'country-residence':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Your primary country of residence</HeaderText>
              <SubtitleText>Select the country where you currently live</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Country</StyledText>
              {COUNTRIES.map((country) => (
                <StyledTouchableOpacity
                  key={country.code}
                  className={`w-full border rounded-xl p-4 mb-3 transition-colors ${
                    formData.countryOfResidence === country.code
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => updateFormData('countryOfResidence', country.code)}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledView className="flex-row items-center">
                      <StyledText className="text-2xl mr-3">{country.flag}</StyledText>
                      <StyledText className={`text-base ${
                        formData.countryOfResidence === country.code ? 'text-secondary font-medium' : 'text-gray-900'
                      }`}>
                        {country.name}
                      </StyledText>
                    </StyledView>
                    {formData.countryOfResidence === country.code && (
                      <Feather name="check" size={20} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
              {formErrors.countryOfResidence ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.countryOfResidence}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'phone-input':
        const selectedCountry = COUNTRIES.find(c => c.code === formData.countryOfResidence);
        const defaultPhoneCode = selectedCountry?.phoneCode || '+1';

        // Set default phone code if not already set
        if (!formData.phoneCountryCode && selectedCountry) {
          updateFormData('phoneCountryCode', defaultPhoneCode);
        }

        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Verify your phone number</HeaderText>
              <SubtitleText>We'll send you a verification code</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Your phone number</StyledText>
              <StyledView className="flex-row space-x-3">
                {/* Country Code Dropdown */}
                <StyledView className="w-20">
                  <StyledTouchableOpacity
                    className={`border rounded-xl p-3 bg-white ${
                      formErrors.phoneCountryCode ? 'border-red-500' : 'border-gray-300'
                    }`}
                    onPress={() => {
                      // For now, cycle through common codes. In production, this would be a proper dropdown
                      const codes = ['+1', '+44', '+49', '+33', '+34', '+39', '+31', '+32', '+41', '+43', '+46', '+47', '+45', '+358', '+65', '+81', '+82', '+353'];
                      const currentIndex = codes.indexOf(formData.phoneCountryCode);
                      const nextIndex = (currentIndex + 1) % codes.length;
                      updateFormData('phoneCountryCode', codes[nextIndex]);
                    }}
                    disabled={isLoading}
                  >
                    <StyledText className="text-center text-base font-medium">
                      {formData.phoneCountryCode || defaultPhoneCode}
                    </StyledText>
                  </StyledTouchableOpacity>
                </StyledView>

                {/* Phone Number Input */}
                <StyledView className="flex-1">
                  <CustomTextInput
                    hasError={!!formErrors.phoneNumber}
                    placeholder="Enter your phone number"
                    value={formData.phoneNumber}
                    onChangeText={(value) => updateFormData('phoneNumber', value)}
                    keyboardType="phone-pad"
                    editable={!isLoading}
                    isWebPlatform={isWebPlatform}
                  />
                </StyledView>
              </StyledView>

              {(formErrors.phoneCountryCode || formErrors.phoneNumber) ? (
                <StyledText className="text-red-500 text-sm mt-1">
                  {formErrors.phoneCountryCode || formErrors.phoneNumber}
                </StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'phone-verification':
        const handleResendCode = () => {
          if (resendTimer === 0) {
            // TODO: Resend SMS code
            setResendTimer(30);
            updateFormData('phonePin', ''); // Clear current PIN
          }
        };

        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Check your phone</HeaderText>
              <SubtitleText>We sent a 4-digit code to {formData.phoneCountryCode} {formData.phoneNumber}</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <StyledText className="text-base font-medium text-gray-700 mb-2">Verification code</StyledText>
              <CustomTextInput
                hasError={!!formErrors.phonePin}
                additionalClasses="text-center text-lg font-mono tracking-widest"
                placeholder="0000"
                value={formData.phonePin}
                onChangeText={(value) => updateFormData('phonePin', value)}
                keyboardType="number-pad"
                maxLength={4}
                editable={!isLoading}
                isWebPlatform={isWebPlatform}
              />
              {formErrors.phonePin ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.phonePin}</StyledText>
              ) : null}

              <StyledTouchableOpacity
                className="mt-4"
                onPress={handleResendCode}
                disabled={resendTimer > 0}
              >
                <StyledText className={`text-sm font-medium ${
                  resendTimer > 0 ? 'text-gray-400' : 'text-secondary'
                }`}>
                  {resendTimer > 0
                    ? `Resend code in ${resendTimer}s`
                    : "Didn't receive a code? Resend"
                  }
                </StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          </StyledView>
        );

      case 'password-creation':
        const getPasswordStrength = (password: string) => {
          let strength = 0;
          if (password.length >= 8) strength++;
          if (/[a-z]/.test(password)) strength++;
          if (/[A-Z]/.test(password)) strength++;
          if (/\d/.test(password)) strength++;
          if (/[^a-zA-Z\d]/.test(password)) strength++;
          return strength;
        };

        const passwordStrength = getPasswordStrength(formData.password);
        const strengthLabels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const strengthColors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a'];

        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Create your password</HeaderText>
              <SubtitleText>Choose a secure password for your account</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {/* Password Input */}
              <StyledView className="mb-4">
                <StyledText className="text-base font-medium text-gray-700 mb-2">Password</StyledText>
                <StyledView className="relative">
                  <CustomTextInput
                    hasError={!!formErrors.password}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChangeText={(value) => updateFormData('password', value)}
                    secureTextEntry={!showPassword}
                    editable={!isLoading}
                    isWebPlatform={isWebPlatform}
                  />
                  <StyledTouchableOpacity
                    className="absolute right-3 top-3"
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Feather name={showPassword ? "eye-off" : "eye"} size={20} color="#6B7280" />
                  </StyledTouchableOpacity>
                </StyledView>
                {formErrors.password ? (
                  <StyledText className="text-red-500 text-sm mt-1">{formErrors.password}</StyledText>
                ) : null}
              </StyledView>

              {/* Password Confirmation */}
              <StyledView className="mb-4">
                <StyledText className="text-base font-medium text-gray-700 mb-2">Confirm Password</StyledText>
                <StyledView className="relative">
                  <CustomTextInput
                    hasError={!!formErrors.passwordConfirmation}
                    placeholder="Confirm your password"
                    value={formData.passwordConfirmation}
                    onChangeText={(value) => updateFormData('passwordConfirmation', value)}
                    secureTextEntry={!showPasswordConfirmation}
                    editable={!isLoading}
                    isWebPlatform={isWebPlatform}
                  />
                  <StyledTouchableOpacity
                    className="absolute right-3 top-3"
                    onPress={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                  >
                    <Feather name={showPasswordConfirmation ? "eye-off" : "eye"} size={20} color="#6B7280" />
                  </StyledTouchableOpacity>
                </StyledView>
                {formErrors.passwordConfirmation ? (
                  <StyledText className="text-red-500 text-sm mt-1">{formErrors.passwordConfirmation}</StyledText>
                ) : null}
              </StyledView>

              {/* Password Requirements */}
              {formData.password.length > 0 && (
                <StyledView className="mt-4 p-4 bg-gray-50 rounded-xl">
                  <StyledText className="text-sm font-medium text-gray-700 mb-2">Password Requirements:</StyledText>
                  <StyledView className="space-y-1">
                    <StyledView className="flex-row items-center">
                      <Feather
                        name={formData.password.length >= 8 ? "check" : "x"}
                        size={16}
                        color={formData.password.length >= 8 ? "#22c55e" : "#ef4444"}
                      />
                      <StyledText className="text-sm text-gray-600 ml-2">At least 8 characters</StyledText>
                    </StyledView>
                    <StyledView className="flex-row items-center">
                      <Feather
                        name={/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password) ? "check" : "x"}
                        size={16}
                        color={/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password) ? "#22c55e" : "#ef4444"}
                      />
                      <StyledText className="text-sm text-gray-600 ml-2">Contains a letter and a number</StyledText>
                    </StyledView>
                  </StyledView>
                </StyledView>
              )}
            </StyledView>
          </StyledView>
        );

      case 'employment':
        const employmentOptions = [
          'Full-time employed',
          'Part-time employed',
          'Freelancer/Self-employed',
          'Student',
          'Unemployed',
          'Retired',
          'Other'
        ];

        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Employment Status</HeaderText>
              <SubtitleText>Select your current employment status</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {employmentOptions.map((option) => (
                <StyledTouchableOpacity
                  key={option}
                  className={`w-full border rounded-xl p-4 mb-3 transition-colors ${
                    formData.employment === option
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => updateFormData('employment', option)}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledText className={`text-base ${
                      formData.employment === option ? 'text-secondary font-medium' : 'text-gray-900'
                    }`}>
                      {option}
                    </StyledText>
                    {formData.employment === option && (
                      <Feather name="check" size={20} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
              {formErrors.employment ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.employment}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'income':
        const incomeOptions = [
          'Up to €1,000',
          '€1,001 - €1,500',
          '€1,501 - €2,000',
          '€2,001 - €2,500',
          '€2,501 - €3,000',
          '€3,001 - €4,000',
          '€4,000+'
        ];

        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Income Range</HeaderText>
              <SubtitleText>Choose your monthly income range</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {incomeOptions.map((option) => (
                <StyledTouchableOpacity
                  key={option}
                  className={`w-full border rounded-xl p-4 mb-3 transition-colors ${
                    formData.income === option
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => updateFormData('income', option)}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-center justify-between">
                    <StyledText className={`text-base ${
                      formData.income === option ? 'text-secondary font-medium' : 'text-gray-900'
                    }`}>
                      {option}
                    </StyledText>
                    {formData.income === option && (
                      <Feather name="check" size={20} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              ))}
              {formErrors.income ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.income}</StyledText>
              ) : null}
            </StyledView>
          </StyledView>
        );

      case 'plan':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Choose Your Plan</HeaderText>
              <SubtitleText>Select the plan that works best for you</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {/* CasaPay Free Plan */}
              <StyledTouchableOpacity
                className={`w-full border rounded-xl p-6 mb-4 transition-colors ${
                  formData.plan === 'free'
                    ? 'border-secondary bg-secondary/10'
                    : 'border-gray-300 bg-white hover:bg-gray-50'
                }`}
                onPress={() => updateFormData('plan', 'free')}
                disabled={isLoading}
              >
                <StyledView className="flex-row items-start justify-between mb-3">
                  <StyledView className="flex-1">
                    <StyledText className={`text-xl font-bold mb-1 ${
                      formData.plan === 'free' ? 'text-secondary' : 'text-gray-900'
                    }`}>
                      CasaPay Free
                    </StyledText>
                    <StyledText className="text-gray-600 text-sm">Basic features for getting started</StyledText>
                  </StyledView>
                  {formData.plan === 'free' && (
                    <Feather name="check" size={24} color="#4ca2f5" />
                  )}
                </StyledView>
                <StyledView className="space-y-2">
                  <StyledText className="text-gray-700 text-sm">• Basic tenant screening</StyledText>
                  <StyledText className="text-gray-700 text-sm">• Standard support</StyledText>
                  <StyledText className="text-gray-700 text-sm">• Basic reporting</StyledText>
                </StyledView>
              </StyledTouchableOpacity>

              {/* CasaPay Premium Plan */}
              <StyledTouchableOpacity
                className={`w-full border rounded-xl p-6 mb-4 transition-colors ${
                  formData.plan === 'premium'
                    ? 'border-secondary bg-secondary/10'
                    : 'border-gray-300 bg-white hover:bg-gray-50'
                }`}
                onPress={() => updateFormData('plan', 'premium')}
                disabled={isLoading}
              >
                <StyledView className="flex-row items-start justify-between mb-3">
                  <StyledView className="flex-1">
                    <StyledText className={`text-xl font-bold mb-1 ${
                      formData.plan === 'premium' ? 'text-secondary' : 'text-gray-900'
                    }`}>
                      CasaPay Premium
                    </StyledText>
                    <StyledText className="text-gray-600 text-sm">Enhanced features for power users</StyledText>
                  </StyledView>
                  {formData.plan === 'premium' && (
                    <Feather name="check" size={24} color="#4ca2f5" />
                  )}
                </StyledView>
                <StyledView className="space-y-2">
                  <StyledText className="text-gray-700 text-sm">• Advanced tenant screening</StyledText>
                  <StyledText className="text-gray-700 text-sm">• Priority support</StyledText>
                  <StyledText className="text-gray-700 text-sm">• Advanced analytics</StyledText>
                  <StyledText className="text-gray-700 text-sm">• Custom integrations</StyledText>
                </StyledView>
              </StyledTouchableOpacity>

              {formErrors.plan ? (
                <StyledText className="text-red-500 text-sm mt-1">{formErrors.plan}</StyledText>
              ) : null}

              {/* Skip Option */}
              <StyledView className="mt-6 pt-6 border-t border-gray-200">
                <SkipCheckbox
                  isChecked={!!formData.skipStates.plan}
                  onToggle={() => toggleSkipStep('plan')}
                  label="Skip for now - I'll choose a plan later"
                />
              </StyledView>
            </StyledView>
          </StyledView>
        );

      case 'kyc-selfie':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Take a Selfie</HeaderText>
              <SubtitleText>Take a clear photo of yourself for identity verification (optional)</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <PhotoCapture
                onCapture={(imageUri) => {
                  updateFormData('kycDocuments', { ...formData.kycDocuments, selfie: imageUri });
                }}
                captureType="selfie"
                title="Upload Your Selfie"
                subtitle="Take a clear, well-lit photo of yourself looking directly at the camera"
                hasPhoto={!!formData.kycDocuments.selfie}
                imageUri={formData.kycDocuments.selfie}
                disabled={isLoading}
              />

              {/* Skip Option */}
              <StyledView className="mt-6 pt-6 border-t border-gray-200">
                <SkipCheckbox
                  isChecked={!!formData.skipStates.kycSelfie}
                  onToggle={() => toggleSkipStep('kycSelfie')}
                  label="Skip for now - I'll complete this later"
                />
              </StyledView>
            </StyledView>
          </StyledView>
        );

      case 'kyc-id-front':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Upload ID Front</HeaderText>
              <SubtitleText>Upload the front side of your government-issued ID (optional)</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <PhotoCapture
                onCapture={(imageUri) => {
                  updateFormData('kycDocuments', { ...formData.kycDocuments, idFront: imageUri });
                }}
                captureType="document"
                title="Upload ID Front"
                subtitle="Upload a clear photo of the front side of your driver's license, passport, or national ID"
                hasPhoto={!!formData.kycDocuments.idFront}
                imageUri={formData.kycDocuments.idFront}
                disabled={isLoading}
              />

              {/* Skip Option */}
              <StyledView className="mt-6 pt-6 border-t border-gray-200">
                <SkipCheckbox
                  isChecked={!!formData.skipStates.kycIdFront}
                  onToggle={() => toggleSkipStep('kycIdFront')}
                  label="Skip for now - I'll complete this later"
                />
              </StyledView>
            </StyledView>
          </StyledView>
        );

      case 'kyc-id-back':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Upload ID Back</HeaderText>
              <SubtitleText>Upload the back side of your government-issued ID (optional)</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              <PhotoCapture
                onCapture={(imageUri) => {
                  updateFormData('kycDocuments', { ...formData.kycDocuments, idBack: imageUri });
                }}
                captureType="document"
                title="Upload ID Back"
                subtitle="Upload a clear photo of the back side of your driver's license, passport, or national ID"
                hasPhoto={!!formData.kycDocuments.idBack}
                imageUri={formData.kycDocuments.idBack}
                disabled={isLoading}
              />

              {/* Skip Option */}
              <StyledView className="mt-6 pt-6 border-t border-gray-200">
                <SkipCheckbox
                  isChecked={!!formData.skipStates.kycIdBack}
                  onToggle={() => toggleSkipStep('kycIdBack')}
                  label="Skip for now - I'll complete this later"
                />
              </StyledView>
            </StyledView>
          </StyledView>
        );

      case 'income-verification':
        return (
          <StyledView className="w-full flex-1">
            <StyledView className="mb-12">
              <HeaderText>Income Verification</HeaderText>
              <SubtitleText>Verify your income for better offers (optional)</SubtitleText>
            </StyledView>

            <StyledView className="mb-8">
              {/* Verification Options */}
              <StyledView className="space-y-4">
                {/* Open Banking Option */}
                <StyledTouchableOpacity
                  className={`w-full border rounded-xl p-6 transition-colors ${
                    formData.incomeVerification.type === 'banking'
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => {
                    updateFormData('incomeVerification', { type: 'banking' });
                    router.push('/(auth)/income-verification/banking/country-selection');
                  }}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-start justify-between">
                    <StyledView className="flex-1">
                      <StyledView className="flex-row items-center mb-2">
                        <Feather name="link" size={20} color="#4ca2f5" />
                        <StyledText className={`text-lg font-bold ml-3 ${
                          formData.incomeVerification.type === 'banking' ? 'text-secondary' : 'text-gray-900'
                        }`}>
                          Connect Your Bank
                        </StyledText>
                      </StyledView>
                      <StyledText className="text-gray-600 text-sm mb-3">
                        Securely connect your bank account for instant verification
                      </StyledText>
                      <StyledView className="space-y-1">
                        <StyledText className="text-gray-700 text-sm">• Instant verification</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Bank-grade security</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Better loan offers</StyledText>
                      </StyledView>
                    </StyledView>
                    {formData.incomeVerification.type === 'banking' && (
                      <Feather name="check" size={24} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>

                {/* Document Upload Option */}
                <StyledTouchableOpacity
                  className={`w-full border rounded-xl p-6 transition-colors ${
                    formData.incomeVerification.type === 'document'
                      ? 'border-secondary bg-secondary/10'
                      : 'border-gray-300 bg-white hover:bg-gray-50'
                  }`}
                  onPress={() => {
                    updateFormData('incomeVerification', { type: 'document' });
                    router.push('/(auth)/income-verification/upload/document-upload');
                  }}
                  disabled={isLoading}
                >
                  <StyledView className="flex-row items-start justify-between">
                    <StyledView className="flex-1">
                      <StyledView className="flex-row items-center mb-2">
                        <Feather name="file-text" size={20} color="#4ca2f5" />
                        <StyledText className={`text-lg font-bold ml-3 ${
                          formData.incomeVerification.type === 'document' ? 'text-secondary' : 'text-gray-900'
                        }`}>
                          Upload Documents
                        </StyledText>
                      </StyledView>
                      <StyledText className="text-gray-600 text-sm mb-3">
                        Upload pay stubs or tax documents for verification
                      </StyledText>
                      <StyledView className="space-y-1">
                        <StyledText className="text-gray-700 text-sm">• Manual review process</StyledText>
                        <StyledText className="text-gray-700 text-sm">• Secure document handling</StyledText>
                        <StyledText className="text-gray-700 text-sm">• 1-2 business days</StyledText>
                      </StyledView>
                    </StyledView>
                    {formData.incomeVerification.type === 'document' && (
                      <Feather name="check" size={24} color="#4ca2f5" />
                    )}
                  </StyledView>
                </StyledTouchableOpacity>
              </StyledView>

              {/* Skip Option */}
              <StyledView className="mt-6 pt-6 border-t border-gray-200">
                <SkipCheckbox
                  isChecked={!!formData.skipStates.incomeVerification}
                  onToggle={() => toggleSkipStep('incomeVerification')}
                  label="Skip for now - I'll complete this later"
                />
              </StyledView>
            </StyledView>
          </StyledView>
        );

      case 'placeholder':
        return (
          <StyledView className="w-full flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Almost there!</HeaderText>
              <SubtitleText>We're setting up your account</SubtitleText>
            </StyledView>

            <StyledView className="items-center py-8">
              <StyledView className="w-20 h-20 bg-gray-100 rounded-full items-center justify-center mb-4">
                <Feather name="settings" size={32} color="#6B7280" />
              </StyledView>
              <StyledText className="text-sm text-gray-500 text-center mb-2">
                This step will be implemented later
              </StyledText>
              <StyledText className="text-xs text-gray-400 text-center">
                Additional account setup features coming soon
              </StyledText>
            </StyledView>
          </StyledView>
        );

      case 'done':
        return (
          <StyledView className="w-full flex-1 justify-center">
            <StyledView className="mb-12">
              <HeaderText>Welcome to CasaPay!</HeaderText>
              <SubtitleText>Your account has been created successfully</SubtitleText>
            </StyledView>

            <StyledView className="items-center py-8">
              <StyledView className="w-20 h-20 bg-secondary/10 rounded-full items-center justify-center mb-4">
                <Feather name="check" size={32} color="#4ca2f5" />
              </StyledView>
              <StyledText className="text-sm text-gray-500 text-center mb-2">
                Account created successfully!
              </StyledText>
              <StyledText className="text-sm text-gray-500 text-center">
                Welcome to CasaPay, {formData.name}
              </StyledText>
            </StyledView>
          </StyledView>
        );

      default:
        return null;
    }
  };

  return (
    <StyledSafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <StyledScrollView className="flex-grow px-6 pb-10">
        <StyledView className="w-full max-w-md mx-auto flex-1">
          {/* Back button */}
          <StyledView className="mb-6 mt-4">
            <TouchableOpacity
              onPress={handleBack}
              className="p-2 self-start"
              disabled={isLoading}
            >
              {currentStep === 'email' ? (
                <Feather name="x" size={24} color="#374151" />
              ) : (
                <Feather name="arrow-left" size={24} color="#374151" />
              )}
            </TouchableOpacity>
          </StyledView>



          {/* Error message */}
          {formErrors.general ? (
            <StyledView className="mb-6 py-3 px-5 bg-red-100 rounded-md">
              <StyledText className="text-red-500 text-sm">{formErrors.general}</StyledText>
            </StyledView>
          ) : null}

          {/* Step content */}
          <StyledView className="flex-1 justify-center">
            {renderStepContent()}
          </StyledView>

          {/* Terms and Privacy for email step */}
          {currentStep === 'email' && (
            <StyledView className="mb-6">
              <StyledView className="flex-row items-center justify-center flex-wrap">
                <StyledText className="text-sm text-gray-500 text-center">
                  By registering, you accept our{' '}
                </StyledText>
                <TouchableOpacity onPress={handleTermsOfUse}>
                  <StyledText className="text-sm text-secondary underline font-medium">Terms of Use</StyledText>
                </TouchableOpacity>
                <StyledText className="text-sm text-gray-500 text-center">
                  {' '}and{' '}
                </StyledText>
                <TouchableOpacity onPress={handlePrivacyPolicy}>
                  <StyledText className="text-sm text-secondary underline font-medium">Privacy Policy</StyledText>
                </TouchableOpacity>
              </StyledView>
            </StyledView>
          )}

          {/* Continue Button */}
          <StyledTouchableOpacity
            className={`rounded-xl py-4 px-6 items-center w-full shadow-md transition-colors ${
              isLoading || !canProceedFromCurrentStep()
                ? 'bg-gray-300 opacity-70'
                : 'bg-secondary hover:bg-secondary/90'
            }`}
            onPress={handleContinue}
            disabled={isLoading || !canProceedFromCurrentStep()}
          >
            {isLoading ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <StyledText className={`text-base font-semibold ${
                !canProceedFromCurrentStep() ? 'text-gray-500' : 'text-white'
              }`}>
                {getCTAText()}
              </StyledText>
            )}
          </StyledTouchableOpacity>

          {/* Login Link for first step */}
          {currentStep === 'email' && (
            <StyledView className="flex-row items-center justify-center mt-8 mb-4">
              <StyledText className="text-sm text-gray-500">Already have an account? </StyledText>
              <StyledTouchableOpacity onPress={() => router.push('/(auth)/login')} disabled={isLoading}>
                <StyledText className="text-sm text-link font-medium">Log in</StyledText>
              </StyledTouchableOpacity>
            </StyledView>
          )}
        </StyledView>
      </StyledScrollView>
    </StyledSafeAreaView>
  );
}
